<?php

namespace app\admin\controller;

use app\admin\model\Order;
use app\common\controller\Backend;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;
use fast\Http;

/**
 * 预授权支付
 *
 * @icon fa fa-circle-o
 */
class Alipaypreauth extends Backend
{
    protected $dataLimit = "auth";

    protected $dataLimitField = "admin_id";

    protected $noNeedRight = ['*'];

    /**
     * Account模型对象
     * @var \app\admin\model\Account
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Account;
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                    ->with(['admin'])
                    ->where($where)
                    ->where('channel','9002')
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
				$row->getRelation('admin')->visible(['nickname']);
                $row['today'] = Order::where('account_identity',$row['account_identity'])
                    ->where('pay_status',1)
                    ->whereTime('create_time', 'today')
                    ->sum('amount');
                $row['today'] = number_format($row['today'],2);
                $row['yesterday'] = Order::where('account_identity',$row['account_identity'])
                    ->where('pay_status',1)
                    ->whereTime('create_time', 'yesterday')
                    ->sum('amount');
                $row['yesterday'] = number_format($row['yesterday'],2);
                $row['beforeday'] = Order::where('account_identity',$row['account_identity'])
                    ->where('pay_status',1)
                    ->whereTime('create_time', 'between',[date('Y-m-d',time()-2*24*3600),date('Y-m-d',time()-1*24*3600)])
                    ->sum('amount');
                $row['beforeday'] = number_format($row['beforeday'],2);
                $ispay = Order::where('account_identity',$row['account_identity'])
                    ->where('pay_status',1)
                    ->whereTime('create_time', 'today')
                    ->count();
                $all = Order::where('account_identity',$row['account_identity'])
                    ->whereTime('create_time', 'today')
                    ->count();
                $row['today_rate'] =  $all==0 ? "0" : number_format(($ispay/$all) * 100,2);
            }
            unset($row);
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {

        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');

        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $params = $this->preExcludeFields($params);
        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        
        // if ($this->model->where('name',$params['name'])->where('channel','9008')->find() != null) {
        //     $this->error("该账号已存在");
        // }
        if ($this->model->where('account_identity',$params['account_identity'])->where('channel','9002')->find() != null) {
            $this->error("该账号已存在");
        }
        // 格式化配置信息
        $params['config'] = json_encode([
            'appid' => $params['appid'],
        ], JSON_THROW_ON_ERROR);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $result = $this->model->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            // 格式化配置信息
            $row['config'] = json_decode($row['config'],true);
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);
        // 格式化配置信息
        $params['config'] = json_encode([
            'appid' => $params['appid'],
        ], JSON_THROW_ON_ERROR);
        unset($params['admin_id']);
        $params['statusinfo'] = '';
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 删除
     *
     * @param $ids
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $row = $this->model->get($ids);
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                $count += $item->delete();
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }

    public function test($ids)
    {
        if ($this->request->isPost()) {
            $ids = $ids ?: $this->request->post("ids");
            if (empty($ids)) {
                $this->error(__('Parameter %s can not be empty', 'ids'));
            }
            $pk = $this->model->getPk();
            $adminIds = $this->getDataLimitAdminIds();
            if (is_array($adminIds)) {
                $this->model->where($this->dataLimitField, 'in', $adminIds);
            }
            $row = $this->model->where($pk,$ids)->find();
            $params = $this->request->post('row/a');

            $out_trade_no = \app\common\library\Order::createUniqueNo();
            $data = [
                'out_trade_no' => $out_trade_no,
                'merchants_code' => 'MC100020_15',
                'amount' => $params['price'],
                'channel_code' => $row["channel"],
                'notify_url' => 'haha',
                'test'=>'true'
            ];
            $sign = md5(urldecode($this->ascii($data)) . "&key=1a04a03e398dde1390e699ada41d4bd1");
            $url = $this->request->domain()."/api/order/newcreate?out_trade_no=".$out_trade_no."&merchants_code=MC100020_15&amount=".$data['amount']."&channel_code=".$data['channel_code']."&notify_url=haha&test=true&sign=".$sign;
            
            $res = Http::post($url);
            // halt($res);
            $res = json_decode($res,true);

            if ($res['code'] == 0){
                $this->error($res['msg']);
            }
            $qr_url = $this->request->domain().'/api/demo/qrcode?codeText='.urlencode($res['data']);
            // $qr_url = 'https://qun.qq.com/qrcode/index?data='.urlencode($res['data']['url']);
            echo '<img src="'.$qr_url.'" alt="二维码" width=300px>';
            die;
            // return $this->redirect($res['data']['url']);
        }

        return $this->view->fetch();
    }
    
    protected function ascii($params = array()){
        unset($params['undefined']);
        unset($params['sign']);
        //ksort()对数组按照键名进行升序排序
        ksort($params);
        //reset()内部指针指向数组中的第一个元素
        reset($params);
        $str = http_build_query($params, '', '&');
        return $str;
    }
    

    /**
     * 批量更新
     *
     * @param $ids
     * @return void
     */
    public function multi($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }
        $ids = $ids ?: $this->request->post('ids');
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        if (false === $this->request->has('params')) {
            $this->error(__('No rows were updated'));
        }
        parse_str($this->request->post('params'), $values);
        $values = $this->auth->isSuperAdmin() ? $values : array_intersect_key($values, array_flip(is_array($this->multiFields) ? $this->multiFields : explode(',', $this->multiFields)));

        if (empty($values)) {
            $this->error(__('You have no permission'));
        }

        if ($values['status'] == 'normal'){
            $values['statusinfo'] = '';
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $count = 0;
        Db::startTrans();
        try {
            $list = $this->model->where($this->model->getPk(), 'in', $ids)->select();
            foreach ($list as $item) {
                $count += $item->allowField(true)->isUpdate(true)->save($values);
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were updated'));
    }

    public function paylimit($ids)
    {
        $ids = $ids ?: $this->request->post('ids');
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }

        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }

        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        $count = 0;
        Db::startTrans();
        try {
            $list = $this->model->where($this->model->getPk(), 'in', $ids)->select();
            foreach ($list as $item) {
                $count += $item->allowField(true)->isUpdate(true)->save(["pay_limit"=>$params['pay_limit']]);
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were updated'));
    }

    public function accountlog($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        $start_time = "2025-05-29 00:00:00";

        $end_time = date("Y-m-d")." 23:59:00";
        // 获取账号信息
        $account = db('account')->where('id', $ids)->find();

        $accountConfig = json_decode($account['config'], true, 512, JSON_THROW_ON_ERROR);
                
        $uri = 'https://openapi.alipay.com/gateway.do';
        $requestConfigs = array(
            'start_time' => $start_time,
            'end_time' => $end_time
        );
        $commonConfigs = array(
            'app_id' => $accountConfig['appid'],
            'method' => 'alipay.data.bill.accountlog.query', //接口名称
            'format' => 'JSON',
            'charset'=> 'utf-8',
            'sign_type'=> 'RSA2',
            'timestamp'=> $start_time,
            'version'=> '1.0',
            'biz_content'=> json_encode($requestConfigs),
        );

        $aliPay = new AlipayService();
        $aliPay->setAppid($accountConfig['appid']);
        $aliPay->setRsaPrivateKey($accountConfig['private_key']);
        $commonConfigs["sign"] = $aliPay->generateSign($commonConfigs, $commonConfigs['sign_type']);
        $x = $aliPay->buildOrderStr($commonConfigs);
        $xxx = Http::get("{$uri}?$x");
        $jsondata = json_decode($xxx,true);
        if(empty($jsondata['alipay_data_bill_accountlog_query_response']['detail_list'])){
            echo '账单为空,支付宝返回值如下：<br>';
            echo halt($jsondata);
        }
        $this->view->assign('data', $jsondata['alipay_data_bill_accountlog_query_response']['detail_list']);
        return $this->view->fetch();
    }
}
